import React, { useState } from 'react';
import Layout from '../layouts/Layout';

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically send the form data to your backend
    console.log('Form submitted:', formData);
    setIsSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: ''
      });
    }, 3000);
  };

  return (
    <Layout>
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-lg-10">
            <header className="text-center mb-5">
              <h1 className="display-4 fw-bold text-primary mb-3">Contact Us</h1>
              <p className="lead text-muted">
                We'd love to hear from you. Get in touch with our team.
              </p>
            </header>

            <div className="row g-5">
              {/* Contact Information */}
              <div className="col-lg-4">
                <div className="h-100">
                  <h3 className="mb-4">Get in Touch</h3>
                  
                  <div className="contact-info">
                    <div className="d-flex align-items-start mb-4">
                      <i className="bi bi-geo-alt-fill text-primary fs-4 me-3 mt-1"></i>
                      <div>
                        <h5>Address</h5>
                        <p className="text-muted mb-0">
                          University of Moratuwa<br />
                          Bandaranayake Mawatha<br />
                          Moratuwa 10400, Sri Lanka
                        </p>
                      </div>
                    </div>

                    <div className="d-flex align-items-start mb-4">
                      <i className="bi bi-telephone-fill text-primary fs-4 me-3 mt-1"></i>
                      <div>
                        <h5>Phone</h5>
                        <p className="text-muted mb-0">+94 11 265 0301</p>
                      </div>
                    </div>

                    <div className="d-flex align-items-start mb-4">
                      <i className="bi bi-envelope-fill text-primary fs-4 me-3 mt-1"></i>
                      <div>
                        <h5>Email</h5>
                        <p className="text-muted mb-0"><EMAIL></p>
                      </div>
                    </div>

                    <div className="d-flex align-items-start">
                      <i className="bi bi-clock-fill text-primary fs-4 me-3 mt-1"></i>
                      <div>
                        <h5>Office Hours</h5>
                        <p className="text-muted mb-0">
                          Monday - Friday: 9:00 AM - 5:00 PM<br />
                          Saturday: 9:00 AM - 1:00 PM<br />
                          Sunday: Closed
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <h5>Follow Us</h5>
                    <div className="d-flex gap-3 mt-3">
                      <a href="#" className="text-primary fs-4">
                        <i className="bi bi-facebook"></i>
                      </a>
                      <a href="#" className="text-primary fs-4">
                        <i className="bi bi-twitter"></i>
                      </a>
                      <a href="#" className="text-primary fs-4">
                        <i className="bi bi-linkedin"></i>
                      </a>
                      <a href="#" className="text-primary fs-4">
                        <i className="bi bi-instagram"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div className="col-lg-8">
                <div className="card border-0 shadow-sm">
                  <div className="card-body p-4">
                    <h3 className="card-title mb-4">Send us a Message</h3>
                    
                    {isSubmitted ? (
                      <div className="alert alert-success d-flex align-items-center" role="alert">
                        <i className="bi bi-check-circle-fill me-2"></i>
                        <div>
                          Thank you for your message! We'll get back to you soon.
                        </div>
                      </div>
                    ) : (
                      <form onSubmit={handleSubmit}>
                        <div className="row g-3">
                          <div className="col-md-6">
                            <label htmlFor="name" className="form-label">Full Name *</label>
                            <input
                              type="text"
                              className="form-control"
                              id="name"
                              name="name"
                              value={formData.name}
                              onChange={handleInputChange}
                              required
                            />
                          </div>
                          <div className="col-md-6">
                            <label htmlFor="email" className="form-label">Email Address *</label>
                            <input
                              type="email"
                              className="form-control"
                              id="email"
                              name="email"
                              value={formData.email}
                              onChange={handleInputChange}
                              required
                            />
                          </div>
                          <div className="col-12">
                            <label htmlFor="subject" className="form-label">Subject *</label>
                            <select
                              className="form-select"
                              id="subject"
                              name="subject"
                              value={formData.subject}
                              onChange={handleInputChange}
                              required
                            >
                              <option value="">Select a subject</option>
                              <option value="general">General Inquiry</option>
                              <option value="technical">Technical Support</option>
                              <option value="feedback">Feedback</option>
                              <option value="partnership">Partnership</option>
                              <option value="other">Other</option>
                            </select>
                          </div>
                          <div className="col-12">
                            <label htmlFor="message" className="form-label">Message *</label>
                            <textarea
                              className="form-control"
                              id="message"
                              name="message"
                              rows={5}
                              value={formData.message}
                              onChange={handleInputChange}
                              placeholder="Tell us how we can help you..."
                              required
                            ></textarea>
                          </div>
                          <div className="col-12">
                            <button type="submit" className="btn btn-primary btn-lg">
                              <i className="bi bi-send me-2"></i>
                              Send Message
                            </button>
                          </div>
                        </div>
                      </form>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Contact;
