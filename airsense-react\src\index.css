:root {
  --primary-color: #4FB8A3;
  --secondary-color: #3A8A98;
  --accent-color: #2E5C6E;
  --background-color: #f8f9fe;
  --card-background: #FFFFFF;
  --text-primary: #2C3E50;
  --text-secondary: #5F6368;
  --success-color: #34A853;
  --warning-color: #FBBC05;
  --danger-color:#EA4335;
  --border-radius: 12px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
    background-image: url('/background.jpg');
    background-attachment: fixed;
    background-size: 100% 100%;
    background-color: var(--background-color);
    color: var(--text-primary);
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    padding-top: 80px;
    overflow-x: hidden;
}

.nav-bar {
  background: white;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  color: rgb(81, 72, 72);
  min-height: 80px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
}

.nav-links {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex-wrap: nowrap;
}

.nav-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  padding: 0.75rem 1.8rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 1.4rem;
  white-space: nowrap;
  display: inline-block;
}

.nav-links a.active,
.nav-links a:hover {
  color: var(--primary-color);
  background: rgba(79, 184, 163, 0.1);
}

/* Mobile menu button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-primary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.mobile-menu-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Ensure navigation text never wraps and consistent alignment */
.nav-links a {
  min-width: max-content;
}

/* Ensure consistent navigation alignment across all pages */
.nav-bar .nav-links {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex-wrap: nowrap;
}

.nav-bar .logo-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.nav-bar .search-container,
.nav-bar .comparison-location-bar {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}

/* Ensure content doesn't hide behind fixed navbar */
main, .hero, .welcome-section {
  margin-top: 0;
  padding-top: 0;
}

/* Special handling for pages with hero sections */
.hero {
  margin-top: 0;
}

.search-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}

.home-icon-btn {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 0.4rem;
}

.home-icon{
  height: 32px;
  width: 32px;
  border: none;
  outline: none;
}

.comparison-location-bar {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}

.location-dropdown {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 1rem;
  cursor: pointer;
  color: black;
  text-transform: capitalize;
  font-weight: 500;
}

/* Dashboard Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1400px;
  margin: 2rem auto;
  padding: 0 2rem;
}

.metric-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  text-align: center;
  transition: transform 0.2s ease;
  width: 100%;
  max-width: 280px;
  min-height: 300px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.metric-card:hover {
  transform: translateY(-3px);
}

.metric-card h3 {
  color: var(--accent-color);
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  flex-shrink: 0;
}

.gauge-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gauge-container {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 1rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-badge {
  margin-top: 1rem;
}

.badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: capitalize;
}

.badge-good {
  background-color: var(--success-color);
  color: white;
}

.badge-moderate {
  background-color: var(--warning-color);
  color: white;
}

.badge-poor {
  background-color: var(--danger-color);
  color: white;
}

.metric-subtitle {
  margin-top: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Quality Scale Card */
.quality-scale-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  max-width: 1400px;
  margin: 2rem auto;
}

.quality-scale-card h2 {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.scale-bar {
  height: 12px;
  background: linear-gradient(to right,
    var(--success-color) 0%,
    var(--warning-color) 50%,
    var(--danger-color) 100%
  );
  border-radius: 6px;
  margin: 1rem 0;
}

.scale-labels {
  display: flex;
  justify-content: space-between;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.9rem;
}

/* Container and Layout */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Welcome Section */
.welcome-section {
  background: linear-gradient(135deg, rgba(79, 184, 163, 0.1), rgba(58, 138, 152, 0.1));
  padding: 3rem 2rem;
  text-align: center;
  margin-bottom: 2rem;
}

.welcome-section h1 {
  color: var(--text-primary);
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.welcome-section .subtitle {
  color: var(--text-secondary);
  font-size: 1.2rem;
  font-weight: 400;
}

/* Comparison Page Styles */
.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1200px;
  margin: 2rem auto;
}

.location-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.location-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

.location-card h3 {
  font-size: 1.5rem;
  color: var(--accent-color);
  margin-bottom: 1.5rem;
  text-align: center;
}

.location-card .gauge-container {
  width: 200px;
  height: 200px;
  margin: 1.5rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-card .status {
  text-align: center;
  margin: 1rem 0;
  font-weight: 500;
  color: var(--text-secondary);
}

.legend {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.chart-error {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 1rem;
  border-radius: var(--border-radius);
  text-align: center;
  margin: 2rem auto;
  max-width: 600px;
}

.comparison-summary {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin: 2rem auto;
  max-width: 1200px;
}

.comparison-summary h3 {
  color: var(--accent-color);
  margin-bottom: 1rem;
}

.comparison-text {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 1rem;
}

.daily-distribution {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin: 1rem auto 0.5rem auto;
  max-width: 1200px;
}

.daily-distribution h3 {
  color: var(--accent-color);
  margin-bottom: 1.5rem;
}

/* Hero Section */
.hero {
  background-image: url('/background.jpg');
  background-attachment: fixed;
  background-size: cover;
  background-position: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
  color: var(--text-primary);
}

.hero h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero p {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Features Section */
.features {
  background: rgba(255, 255, 255, 0.95);
  padding: 4rem 2rem;
  margin: 2rem 0;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.container1 {
  max-width: 1200px;
  margin: 0 auto;
}

.feature-item {
  padding: 2rem;
}

.feature-item h3 {
  color: var(--accent-color);
  font-size: 2rem;
  margin-bottom: 1.5rem;
}

.feature-item p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-top: 1.5rem;
}

/* Chart Container Improvements */
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

/* Form Controls */
.form-select, .form-control {
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus, .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(79, 184, 163, 0.25);
  outline: 0;
}

.form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

/* Button Styles */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  transform: translateY(-1px);
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Alert Styles */
.alert {
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  border: 1px solid transparent;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.alert-warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .nav-links {
    gap: 1.5rem;
  }

  .nav-links a {
    padding: 0.6rem 1.4rem;
    font-size: 1.2rem;
    white-space: nowrap;
  }

  .container {
    max-width: 100%;
    padding: 0 1rem;
  }
}

@media (max-width: 1000px) {
  .nav-bar {
    padding: 15px 15px;
  }

  .nav-links {
    gap: 1.2rem;
  }

  .nav-links a {
    padding: 0.5rem 1rem;
    font-size: 1rem;
    white-space: nowrap;
  }

  .logo {
    height: 60px;
  }
}

@media (max-width: 900px) {
  .nav-bar {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem;
    min-height: auto;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  body {
    padding-top: 120px;
  }

  .logo-container {
    flex: 1;
    order: 1;
  }

  .mobile-menu-btn {
    display: block !important;
    order: 2;
  }

  .nav-links {
    order: 3;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 0 0 8px 8px;
    padding: 1rem;
    gap: 0.5rem;
    width: 100%;
    display: none;
    z-index: 1001;
  }

  .nav-links.mobile-open {
    display: flex;
  }

  .nav-links a {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    text-align: center;
    border-radius: 6px;
    width: 100%;
  }

  .search-container, .comparison-location-bar {
    order: 4;
    justify-content: center;
    width: 100%;
    margin-top: 1rem;
  }

  .location-dropdown {
    width: 200px;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    padding: 0 1rem;
  }

  .container {
    padding: 0 1rem;
  }

  .welcome-section {
    padding: 2rem 1rem;
  }

  .welcome-section h1 {
    font-size: 2rem;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .nav-bar {
    padding: 0.75rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  body {
    padding-top: 140px;
  }

  .nav-links {
    gap: 0.25rem;
  }

  .nav-links a {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }

  .logo {
    height: 50px;
  }

  .location-dropdown {
    width: 150px;
    font-size: 0.8rem;
  }

  .home-icon {
    height: 24px;
    width: 24px;
  }

  .hero h1 {
    font-size: 1.5rem;
  }

  .welcome-section h1 {
    font-size: 1.5rem;
  }

  .container {
    padding: 0 0.5rem;
  }

  /* About and Contact page responsive styles */
  .container {
    padding: 0 1rem;
    max-width: 100%;
  }

  .py-5 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  .card {
    margin-bottom: 1rem;
  }

  .card-body {
    padding: 1rem !important;
  }

  .display-4 {
    font-size: 1.8rem !important;
  }

  .lead {
    font-size: 1rem !important;
  }

  .row.g-4 > * {
    margin-bottom: 1rem;
  }

  .row.g-5 > * {
    margin-bottom: 1.5rem;
  }

  /* About page specific responsive styles */
  .col-md-4, .col-md-6 {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }

  .d-flex.align-items-center {
    flex-direction: column !important;
    text-align: center !important;
  }

  .d-flex.align-items-center i {
    margin-bottom: 0.5rem !important;
    margin-right: 0 !important;
  }

  .d-flex.align-items-center h3,
  .d-flex.align-items-center h5 {
    margin-bottom: 0.5rem !important;
  }

  .contact-info .d-flex {
    flex-direction: column;
    text-align: center;
    margin-bottom: 2rem;
  }

  .contact-info .d-flex i {
    margin-bottom: 0.5rem;
    margin-right: 0;
  }

  /* Form responsive styles */
  .form-control, .form-select {
    font-size: 1rem;
  }

  .btn-lg {
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
  }

  /* Icon adjustments for mobile */
  .fs-1 {
    font-size: 2rem !important;
  }

  .fs-2 {
    font-size: 1.5rem !important;
  }

  .fs-4 {
    font-size: 1.2rem !important;
  }

  /* Text center for mobile */
  .text-center p {
    text-align: center !important;
  }

  /* Card spacing */
  .mb-5 {
    margin-bottom: 2rem !important;
  }

  .mb-4 {
    margin-bottom: 1.5rem !important;
  }

  .mb-3 {
    margin-bottom: 1rem !important;
  }

  /* About page grid responsive */
  .row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .row > * {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
}

/* Additional responsive styles for About page */
@media (max-width: 992px) {
  .col-lg-10 {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }

  .container {
    padding: 0 1rem;
  }

  .display-4 {
    font-size: 2rem !important;
  }

  .card-body {
    padding: 1.5rem !important;
  }
}

@media (max-width: 768px) {
  .py-5 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  .display-4 {
    font-size: 1.75rem !important;
  }

  .lead {
    font-size: 1rem !important;
  }

  .card-body {
    padding: 1rem !important;
  }

  .fs-2 {
    font-size: 1.25rem !important;
  }

  .card-title {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }

  .py-5 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .display-4 {
    font-size: 1.5rem !important;
  }

  .lead {
    font-size: 0.9rem !important;
  }

  .card-body {
    padding: 0.75rem !important;
  }

  .card-title {
    font-size: 1.1rem !important;
  }

  .card-text {
    font-size: 0.9rem !important;
  }

  .fs-1 {
    font-size: 1.5rem !important;
  }

  .fs-2 {
    font-size: 1.1rem !important;
  }

  .btn-lg {
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
  }
}

/* About page specific layout fixes */
.about-page .container {
  width: 100%;
  max-width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}

.about-page .row {
  margin-left: -15px;
  margin-right: -15px;
}

.about-page .row > * {
  padding-left: 15px;
  padding-right: 15px;
}

/* Ensure cards are responsive */
.card {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.card .d-flex {
  flex-wrap: wrap;
}

/* Bootstrap responsive overrides */
@media (max-width: 767.98px) {
  .col-md-6, .col-md-4 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .g-4 > * {
    margin-bottom: 1rem;
  }

  .g-5 > * {
    margin-bottom: 1.5rem;
  }
}

/* Ensure proper spacing on all devices */
.text-center {
  padding-left: 1rem;
  padding-right: 1rem;
}

.bg-light {
  padding: 1rem;
  margin: 1rem 0;
}

.logo-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo {
  height: 80px;
  width: auto;
  border-radius: 10px;
}



.auth-buttons {
  display: flex;
  gap: 1rem;
}

.user-info-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #dbdddf;
  border-bottom: 1px solid #dee2e6;
}

.hero {
    text-align: center;
    padding: 50px 20px;
    background: url('/background.jpg') no-repeat center center/cover;
    color: #333;
}

.hero h1 {
    font-size: 50px;
    margin-bottom: 10px;
}

.hero h2 {
    font-size: 45px;
    font-weight: bold;
    color: #090909;
}

.hero p {
    font-size: 25px;
    margin-bottom: 20px;
}

.real-time-monitoring {
    margin: 20px auto;
    padding: 15px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
    max-width: 400px;
    border-radius: 8px;
}

footer {
    text-align: center;
    padding: 20px 0;
    background-color: #f1f1f1;
    color: #555;
    font-size: 16px;
}

.logo {
    height: 80px;
}

.features {
    background-color: #dfdede;
    padding: 2rem 1rem;
}

.features h2 {
    font-family: 'Poppins', sans-serif;
    font-size:40px;
    text-align: center;
    margin-bottom: 1.5rem;
}

.feature-item {
    background-color: #F7F7F7;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.feature-item h3 {
    font-weight:bold ;
    font-family: 'Poppins', sans-serif;
    font-size: 25px;
    color: #2A3E59;
    margin-bottom: 0.5rem;
}

.feature-item p {
    font-size: 20px;
    color: #555555;
}

.carousel-indicators button {
    background-color: #32B3A4;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: #32B3A4;
    border-radius: 50%;
    width: 30px;
    height: 30px;
}

.footer {
    background-color: #2c3e50;
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.footer-links {
    margin: 1rem 0;
}

.footer-links a {
    color: #bbbebd;
    text-decoration: none;
    margin: 0 1rem;
    font-weight: bold;
}

.footer-links a:hover {
    text-decoration: underline;
}

.footer-social-icons {
    margin-top: 1rem;
}

.footer-social-icons img {
    height: 50px;
    width: 50px;
    margin: 0 0.5rem;
    vertical-align: middle;
    border-radius: 10px;
    gap: 300px;
}

.container {
  background-color: #FFFFFF;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  width: 400px;
  max-width: 90%;
  text-align: center;
}

.input-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.input-group label {
  display: block;
  font-weight: 500;
  color: #2A3E59;
  margin-bottom: 0.5rem;
}

.input-group input, .input-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #32B3A4;
  border-radius: 5px;
  font-size: 1rem;
}

.input-group input:focus, .input-group select:focus {
  outline: none;
  border-color: #27988A;
  box-shadow: 0 0 5px rgba(50, 179, 164, 0.5);
}

.btn-primary {
  display: inline-block;
  background-color: #32B3A4;
  color: #FFFFFF;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-primary:hover {
  background-color: #27988A;
}

.hidden {
  display: none;
}

.view {
  display: block;
}

/* Dashboard Styles */
.welcome-section {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, rgba(79, 184, 163, 0.1), rgba(46, 92, 110, 0.1));
}

.welcome-section h1 {
  font-size: 2.5rem;
  color: var(--accent-color);
  margin-bottom: 1rem;
}

.welcome-section .subtitle {
  font-size: 1.3rem;
  color: var(--text-secondary);
}

/* Main container for dashboard and comparison */
main.container, main.dashboard-container {
  width: 100%;
  max-width: 95%;
  margin: 0 auto;
  padding: 1rem;
  box-sizing: border-box;
}

.quality-scale-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  max-width: 1400px;
  margin: 2rem auto;
}

.quality-scale-card h2 {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.scale-bar {
  height: 8px;
  background: linear-gradient(to right,
      #34A853,  /* Good - Green */
      #FBBC05,  /* Moderate - Yellow */
      #EA4335   /* Poor - Red */
  );
  border-radius: 4px;
  margin: 1rem 0;
}

.scale-labels {
  display: flex;
  justify-content: space-between;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.9rem;
}





.metric-value {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 1rem 0;
}

.metric-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.8rem;
}

.status-good {
  background-color: var(--success-color);
  color: white;
}

.status-moderate {
  background-color: var(--warning-color);
  color: white;
}

.status-poor {
  background-color: var(--danger-color);
  color: white;
}

.trends-section {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin: 2rem 0;
}

.trends-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.trends-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.control-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.chart-container {
  height: 400px;
  position: relative;
}



.location-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
}

.location-card h3 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.location-select, .date-picker input, .metrics-select select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.gauge-container {
  text-align: center;
  margin: 2rem 0;
  position: relative;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.value-display {
  font-size: 3rem;
  font-weight: bold;
  color: var(--text-primary);
}

.status {
  text-align: center;
  margin: 1rem 0;
  font-weight: 500;
  color: var(--text-secondary);
}

.legend {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.dot.good {
  background-color: var(--success-color);
}

.dot.moderate {
  background-color: var(--warning-color);
}

.dot.poor {
  background-color: var(--danger-color);
}

.comparison-summary {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin: 2rem 0;
}

.comparison-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}



.validation-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  border: 1px solid #f5c6cb;
}

.chart-error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  text-align: center;
}

/* Location dropdown styles */
.comparison-location-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.location-dropdown:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
}

/* Additional Dashboard Styles */
.metric-card-hover:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

.refresh-button {
  transition: all 0.3s ease;
}

.refresh-button:hover {
  transform: rotate(180deg);
}

/* AQI specific styling */
.aqi-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.aqi-card .card-title {
  color: rgba(255, 255, 255, 0.9);
}

/* Status indicators */
.status-indicator {
  position: relative;
  display: inline-block;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -15px;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

/* Comparison page enhancements */
.location-card {
  transition: box-shadow 0.3s ease;
}

.location-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}



/* Chart container improvements */
.chart-container {
  background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* Loading animations */
.loading-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Enhanced buttons */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 184, 163, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-bar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .nav-links {
    justify-content: center;
  }

  .search-container, .comparison-location-bar {
    justify-content: center;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .hero h2 {
    font-size: 1.8rem;
  }

  .hero p {
    font-size: 1.2rem;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .trends-header {
    flex-direction: column;
    align-items: stretch;
  }

  .trends-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .gauge-container {
    width: 150px;
    height: 150px;
  }



  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}
